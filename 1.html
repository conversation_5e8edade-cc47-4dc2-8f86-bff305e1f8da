<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartInterview AI智能面试系统用户使用说明</title>
    <style>
        body {
            font-family: "宋体", SimSun, serif;
            font-size: 12pt;
            line-height: 1.5;
            margin: 40px;
            background-color: #ffffff;
        }
        
        .title-level-1 {
            font-family: "宋体", SimSun, serif;
            font-size: 12pt;
            line-height: 33pt;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
        }
        
        .title-level-2 {
            font-family: "宋体", SimSun, serif;
            font-size: 12pt;
            line-height: 30pt;
            font-weight: bold;
            margin-bottom: 0.5em;
            margin-top: 1em;
        }
        
        .content {
            font-family: "宋体", SimSun, serif;
            font-size: 12pt;
            text-align: justify;
            text-indent: 2em;
            line-height: 1.8;
            margin: 0.5em 0;
        }
        
        .content-no-indent {
            font-family: "宋体", Sim<PERSON>un, serif;
            font-size: 12pt;
            text-align: justify;
            line-height: 1.8;
            margin: 0.5em 0;
        }
    </style>
</head>
<body>
    <div class="title-level-1">SmartInterview AI智能面试系统用户使用说明</div>
    
    <div class="content-no-indent">在SmartInterview AI智能面试系统中，提供五个核心模块，分别是智慧首页、智慧面试、智慧评估、智慧学习、个人中心，下面是详细使用指南。</div>
    
    <div class="title-level-2">智慧首页</div>
    
    <div class="title-level-2">AI智能助手</div>
    <div class="content">AI智能助手提供7种专业的AI Agent服务，分别是面试官AI、简历分析师AI、技能评估师AI、职业顾问AI、模拟面试官AI、反馈分析师AI、学习指导师AI。用户可以通过点击顶部导航栏的AI选择器来切换不同的AI助手，每个AI助手都有专门的图标和颜色标识。在对话界面中，用户可以输入问题或需求，AI助手会根据其专业领域提供相应的指导和建议。</div>
    
    <div class="title-level-2">智能对话功能</div>
    <div class="content">智能对话功能支持实时文本交流，用户在底部输入框中输入问题后，系统会通过流式响应的方式实时显示AI的回复内容。对话界面支持历史记录查看，用户可以通过侧边栏访问之前的对话会话。同时支持附件上传功能，用户可以上传简历、作品集等文件供AI分析。系统还提供扩展功能入口，方便用户快速访问其他相关工具。</div>
    
    <div class="title-level-2">智慧面试</div>
    
    <div class="title-level-2">快速开始面试</div>
    <div class="content">快速开始面试功能允许用户选择目标岗位后立即开始模拟面试。用户首先需要在岗位选择页面中选择合适的职位类别，如前端开发、后端开发、产品经理等。选择完成后，系统会自动生成针对该岗位的面试问题集。进入面试房间后，系统会按照真实面试流程进行，包括自我介绍、技术问题、行为面试等环节。</div>
    
    <div class="title-level-2">面试房间功能</div>
    <div class="content">面试房间提供完整的模拟面试环境，包含问题展示、计时功能、录制功能等。系统会逐一展示面试问题，每个问题都有建议的回答时间和评估要点提示。用户可以选择开启录制功能来记录自己的回答，便于后续回顾和改进。面试过程中，系统会实时显示剩余时间和当前进度，帮助用户合理分配时间。</div>
    
    <div class="title-level-2">面试记录管理</div>
    <div class="content">面试记录管理功能帮助用户查看和分析历史面试表现。用户可以在面试记录页面查看所有参与过的模拟面试，包括面试时间、岗位类型、总体评分、用时等信息。点击具体记录可以查看详细的面试报告，包括各个问题的回答质量评估、改进建议、关键技能点分析等。系统还会生成能力雷达图，直观展示用户在不同维度的表现。</div>
    
    <div class="title-level-2">智慧评估</div>
    
    <div class="title-level-2">初始能力评估</div>
    <div class="content">初始能力评估功能通过科学的测评问卷帮助用户了解自身的面试能力水平。评估包含专业知识、逻辑思维、语言表达、抗压能力、团队协作、创新能力等六个维度。问卷采用多种题型，包括单选题、多选题和评分题。用户需要根据实际情况如实回答，系统会根据答案计算各维度得分和综合评分。</div>
    
    <div class="title-level-2">评估结果分析</div>
    <div class="content">评估结果分析页面以可视化的方式展示用户的能力评估结果。页面中心显示综合评分的环形进度图，周围展示各个能力维度的雷达图。系统会根据评估结果生成个性化的能力分析报告，指出用户的优势领域和需要改进的方面。同时提供针对性的学习建议和训练计划，帮助用户有针对性地提升面试能力。</div>
    
    <div class="title-level-2">智慧学习</div>
    
    <div class="title-level-2">学习资源中心</div>
    <div class="content">学习资源中心提供丰富的面试技能提升资源，包括视频教程、文章阅读、实战练习等多种形式。资源按照不同的技能类别进行分类，如沟通技巧、技术面试、行为面试、职业规划等。用户可以根据自己的需求和评估结果选择合适的学习内容。每个学习资源都标注了预计学习时长和难度等级。</div>
    
    <div class="title-level-2">章节式学习</div>
    <div class="content">章节式学习功能将复杂的学习内容分解为多个易于掌握的章节。每个学习课程都包含多个章节，用户可以按照顺序逐步学习，也可以根据需要跳转到特定章节。系统会自动记录学习进度，显示每个章节的完成状态。对于视频类型的章节，支持播放进度记录和断点续播功能。</div>
    
    <div class="title-level-2">学习进度跟踪</div>
    <div class="content">学习进度跟踪功能帮助用户监控自己的学习状况和成长轨迹。系统会记录用户的学习时长、完成的课程数量、掌握的技能点等数据。通过数据可视化的方式展示学习趋势和成果，激励用户持续学习。同时提供学习提醒功能，帮助用户养成良好的学习习惯。</div>
    
    <div class="title-level-2">个人中心</div>
    
    <div class="title-level-2">个人资料管理</div>
    <div class="content">个人资料管理功能允许用户维护和更新个人信息，包括基本信息、教育背景、工作经历、技能特长等。完善的个人资料有助于AI助手提供更加精准的个性化建议。用户可以上传头像、编辑个人简介，设置求职意向等。系统会根据用户的背景信息推荐合适的面试岗位和学习资源。</div>
    
    <div class="title-level-2">简历管理</div>
    <div class="content">简历管理功能提供专业的简历创建和优化服务。用户可以使用系统提供的简历模板快速创建简历，也可以上传现有简历进行分析和优化。AI简历分析师会对简历内容进行深度分析，提供关键词优化、格式调整、内容完善等建议。支持多版本简历管理，用户可以针对不同岗位创建定制化的简历版本。</div>
    
    <div class="title-level-2">成长记录</div>
    <div class="content">成长记录功能记录用户在平台上的学习和面试历程。包括能力提升轨迹、面试表现变化、学习成果统计等。系统会生成个人成长报告，展示用户从初次使用到当前的能力发展情况。通过时间轴的形式展示重要的学习里程碑和面试突破，帮助用户直观了解自己的进步过程。</div>
    
    <div class="title-level-2">设置与偏好</div>
    <div class="content">设置与偏好功能允许用户个性化定制应用体验。包括通知设置、隐私设置、学习提醒、界面主题等选项。用户可以设置学习目标和提醒频率，选择感兴趣的行业和岗位类型，调整AI助手的回复风格等。系统还提供数据导出功能，用户可以导出自己的学习记录和面试报告。</div>
    
</body>
</html>  你 先熟悉一下项目