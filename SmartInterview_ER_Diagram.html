<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartInterview AI智能面试系统 - 主要功能ER图</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .title {
            text-align: center;
            color: #2c3e50;
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 30px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
        }
        
        .er-diagram {
            position: relative;
            width: 100%;
            height: 1000px;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .entity {
            position: absolute;
            background: white;
            border: 2px solid #3498db;
            border-radius: 8px;
            padding: 15px;
            min-width: 180px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .entity:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.15);
        }
        
        .entity-title {
            font-weight: bold;
            font-size: 16px;
            color: #2c3e50;
            text-align: center;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 2px solid #3498db;
        }
        
        .entity-attributes {
            font-size: 12px;
            color: #34495e;
            line-height: 1.6;
        }
        
        .pk {
            color: #e74c3c;
            font-weight: bold;
        }
        
        .fk {
            color: #f39c12;
            font-weight: bold;
        }
        
        /* 实体位置定义 */
        .user { top: 50px; left: 50px; background: #ecf0f1; border-color: #34495e; }
        .user-profile { top: 50px; left: 280px; background: #e8f5e8; border-color: #27ae60; }
        .ai-agent { top: 200px; left: 50px; background: #fef9e7; border-color: #f1c40f; }
        .ai-session { top: 200px; left: 280px; background: #fef9e7; border-color: #f39c12; }
        .ai-conversation { top: 200px; left: 510px; background: #fef9e7; border-color: #e67e22; }
        
        .interview { top: 380px; left: 50px; background: #ebf3fd; border-color: #3498db; }
        .interview-question { top: 380px; left: 280px; background: #ebf3fd; border-color: #2980b9; }
        .interview-answer { top: 380px; left: 510px; background: #ebf3fd; border-color: #1abc9c; }
        .interview-record { top: 380px; left: 740px; background: #ebf3fd; border-color: #16a085; }
        
        .skill-category { top: 580px; left: 50px; background: #fdf2e9; border-color: #e67e22; }
        .skill-assessment { top: 580px; left: 280px; background: #fdf2e9; border-color: #d35400; }
        .assessment-result { top: 580px; left: 510px; background: #fdf2e9; border-color: #c0392b; }
        
        .resume { top: 750px; left: 50px; background: #f4ecf7; border-color: #8e44ad; }
        .resume-analysis { top: 750px; left: 280px; background: #f4ecf7; border-color: #9b59b6; }
        
        .course { top: 750px; left: 510px; background: #eafaf1; border-color: #27ae60; }
        .chapter { top: 750px; left: 740px; background: #eafaf1; border-color: #2ecc71; }
        .learning-progress { top: 750px; left: 970px; background: #eafaf1; border-color: #1e8449; }
        
        /* 关系线条 */
        .relationship {
            position: absolute;
            background: #7f8c8d;
            z-index: 1;
        }
        
        .relationship-label {
            position: absolute;
            background: white;
            padding: 2px 6px;
            font-size: 10px;
            color: #2c3e50;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
            z-index: 2;
        }
        
        .legend {
            position: absolute;
            top: 20px;
            right: 20px;
            background: white;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            padding: 15px;
            font-size: 12px;
        }
        
        .legend-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .legend-item {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">SmartInterview AI智能面试系统 - 主要功能ER图</div>
        
        <div class="er-diagram">
            <!-- 用户管理模块 -->
            <div class="entity user">
                <div class="entity-title">User (用户)</div>
                <div class="entity-attributes">
                    <div class="pk">user_id (PK)</div>
                    <div>username</div>
                    <div>email</div>
                    <div>phone</div>
                    <div>password</div>
                    <div>created_time</div>
                    <div>status</div>
                </div>
            </div>
            
            <div class="entity user-profile">
                <div class="entity-title">UserProfile (用户档案)</div>
                <div class="entity-attributes">
                    <div class="pk">profile_id (PK)</div>
                    <div class="fk">user_id (FK)</div>
                    <div>real_name</div>
                    <div>avatar</div>
                    <div>education</div>
                    <div>work_experience</div>
                    <div>target_position</div>
                    <div>skills</div>
                </div>
            </div>
            
            <!-- AI Agent模块 -->
            <div class="entity ai-agent">
                <div class="entity-title">AIAgent (AI代理)</div>
                <div class="entity-attributes">
                    <div class="pk">agent_id (PK)</div>
                    <div>agent_type</div>
                    <div>agent_name</div>
                    <div>description</div>
                    <div>capabilities</div>
                    <div>status</div>
                </div>
            </div>
            
            <div class="entity ai-session">
                <div class="entity-title">AISession (AI会话)</div>
                <div class="entity-attributes">
                    <div class="pk">session_id (PK)</div>
                    <div class="fk">user_id (FK)</div>
                    <div class="fk">agent_id (FK)</div>
                    <div>session_title</div>
                    <div>context_data</div>
                    <div>created_time</div>
                    <div>status</div>
                </div>
            </div>
            
            <div class="entity ai-conversation">
                <div class="entity-title">AIConversation (AI对话)</div>
                <div class="entity-attributes">
                    <div class="pk">conversation_id (PK)</div>
                    <div class="fk">session_id (FK)</div>
                    <div>message_type</div>
                    <div>content</div>
                    <div>metadata</div>
                    <div>timestamp</div>
                </div>
            </div>
            
            <!-- 面试管理模块 -->
            <div class="entity interview">
                <div class="entity-title">Interview (面试)</div>
                <div class="entity-attributes">
                    <div class="pk">interview_id (PK)</div>
                    <div class="fk">user_id (FK)</div>
                    <div>interview_type</div>
                    <div>position_type</div>
                    <div>start_time</div>
                    <div>duration</div>
                    <div>status</div>
                </div>
            </div>
            
            <div class="entity interview-question">
                <div class="entity-title">InterviewQuestion (面试问题)</div>
                <div class="entity-attributes">
                    <div class="pk">question_id (PK)</div>
                    <div class="fk">interview_id (FK)</div>
                    <div>question_text</div>
                    <div>question_type</div>
                    <div>difficulty_level</div>
                    <div>expected_keywords</div>
                    <div>time_limit</div>
                </div>
            </div>
            
            <div class="entity interview-answer">
                <div class="entity-title">InterviewAnswer (面试回答)</div>
                <div class="entity-attributes">
                    <div class="pk">answer_id (PK)</div>
                    <div class="fk">question_id (FK)</div>
                    <div>answer_text</div>
                    <div>answer_time</div>
                    <div>score</div>
                    <div>evaluation</div>
                    <div>feedback</div>
                </div>
            </div>
            
            <div class="entity interview-record">
                <div class="entity-title">InterviewRecord (面试记录)</div>
                <div class="entity-attributes">
                    <div class="pk">record_id (PK)</div>
                    <div class="fk">interview_id (FK)</div>
                    <div>overall_score</div>
                    <div>evaluation_result</div>
                    <div>improvement_suggestions</div>
                    <div>created_time</div>
                </div>
            </div>
            
            <!-- 技能评估模块 -->
            <div class="entity skill-category">
                <div class="entity-title">SkillCategory (技能分类)</div>
                <div class="entity-attributes">
                    <div class="pk">category_id (PK)</div>
                    <div>category_name</div>
                    <div>description</div>
                    <div>weight</div>
                </div>
            </div>
            
            <div class="entity skill-assessment">
                <div class="entity-title">SkillAssessment (技能评估)</div>
                <div class="entity-attributes">
                    <div class="pk">assessment_id (PK)</div>
                    <div class="fk">user_id (FK)</div>
                    <div class="fk">category_id (FK)</div>
                    <div>assessment_date</div>
                    <div>overall_score</div>
                    <div>level_achieved</div>
                </div>
            </div>
            
            <div class="entity assessment-result">
                <div class="entity-title">AssessmentResult (评估结果)</div>
                <div class="entity-attributes">
                    <div class="pk">result_id (PK)</div>
                    <div class="fk">assessment_id (FK)</div>
                    <div>skill_name</div>
                    <div>score</div>
                    <div>max_score</div>
                    <div>performance_level</div>
                </div>
            </div>
            
            <!-- 简历管理模块 -->
            <div class="entity resume">
                <div class="entity-title">Resume (简历)</div>
                <div class="entity-attributes">
                    <div class="pk">resume_id (PK)</div>
                    <div class="fk">user_id (FK)</div>
                    <div>resume_name</div>
                    <div>file_path</div>
                    <div>content</div>
                    <div>created_time</div>
                    <div>status</div>
                </div>
            </div>
            
            <div class="entity resume-analysis">
                <div class="entity-title">ResumeAnalysis (简历分析)</div>
                <div class="entity-attributes">
                    <div class="pk">analysis_id (PK)</div>
                    <div class="fk">resume_id (FK)</div>
                    <div>analysis_result</div>
                    <div>suggestions</div>
                    <div>keyword_match</div>
                    <div>analysis_time</div>
                </div>
            </div>
            
            <!-- 学习管理模块 -->
            <div class="entity course">
                <div class="entity-title">Course (课程)</div>
                <div class="entity-attributes">
                    <div class="pk">course_id (PK)</div>
                    <div>course_name</div>
                    <div>description</div>
                    <div>difficulty_level</div>
                    <div>duration</div>
                    <div>category</div>
                </div>
            </div>
            
            <div class="entity chapter">
                <div class="entity-title">Chapter (章节)</div>
                <div class="entity-attributes">
                    <div class="pk">chapter_id (PK)</div>
                    <div class="fk">course_id (FK)</div>
                    <div>chapter_name</div>
                    <div>content</div>
                    <div>order_index</div>
                    <div>duration</div>
                </div>
            </div>
            
            <div class="entity learning-progress">
                <div class="entity-title">LearningProgress (学习进度)</div>
                <div class="entity-attributes">
                    <div class="pk">progress_id (PK)</div>
                    <div class="fk">user_id (FK)</div>
                    <div class="fk">course_id (FK)</div>
                    <div class="fk">chapter_id (FK)</div>
                    <div>progress_percentage</div>
                    <div>completion_time</div>
                    <div>last_access_time</div>
                </div>
            </div>
            
            <!-- 图例 -->
            <div class="legend">
                <div class="legend-title">图例说明</div>
                <div class="legend-item"><span class="pk">PK</span> - 主键</div>
                <div class="legend-item"><span class="fk">FK</span> - 外键</div>
                <div class="legend-item">实体间连线表示关系</div>
                <div class="legend-item">颜色区分不同功能模块</div>
            </div>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <h3 style="color: #2c3e50; margin-bottom: 15px;">主要实体关系说明</h3>
            <ul style="color: #34495e; line-height: 1.8;">
                <li><strong>用户管理：</strong>User 与 UserProfile 一对一关系，用户可以有多个简历、面试记录等</li>
                <li><strong>AI Agent系统：</strong>一个AI代理可以处理多个会话，每个会话包含多条对话记录</li>
                <li><strong>面试系统：</strong>一次面试包含多个问题，每个问题可以有多个回答，面试有对应的记录</li>
                <li><strong>技能评估：</strong>按技能分类进行评估，每次评估产生多个结果项</li>
                <li><strong>学习系统：</strong>课程包含多个章节，用户通过学习进度表与课程章节关联</li>
                <li><strong>简历系统：</strong>用户可以有多个简历，每个简历可以进行多次分析</li>
            </ul>
        </div>
    </div>
</body>
</html>
